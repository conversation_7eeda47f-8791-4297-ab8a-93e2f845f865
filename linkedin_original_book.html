<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>询盘倍增器：领英外贸高手进阶</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.8;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        
        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }
        
        .confidential {
            color: #ffeb3b;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .year {
            color: rgba(255,255,255,0.8);
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .main-title {
            font-size: clamp(24px, 5vw, 36px);
            font-weight: bold;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        /* 导航栏 */
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-toggle {
            display: none;
            background: #0077b5;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .nav-search {
            flex: 1;
            max-width: 300px;
            margin: 0 20px;
        }
        
        .nav-search input {
            width: 100%;
            padding: 8px 15px;
            border: 2px solid #0077b5;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .nav-tools {
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            background: #0077b5;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .nav-btn:hover {
            background: #005885;
        }
        
        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(100vh - 200px);
        }
        
        /* 侧边栏目录 */
        .sidebar {
            width: 350px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            position: sticky;
            top: 80px;
        }
        
        .toc {
            padding: 20px;
        }
        
        .toc h3 {
            color: #0077b5;
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #0077b5;
            padding-bottom: 10px;
        }
        
        .part {
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .part-title {
            background: #0077b5;
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .part-title:hover {
            background: #005885;
        }
        
        .chapters {
            background: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .chapters.expanded {
            max-height: 1000px;
        }
        
        .chapter {
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
        }
        
        .chapter:hover {
            background: #e3f2fd;
            padding-left: 20px;
        }
        
        .chapter:last-child {
            border-bottom: none;
        }
        
        .chapter-number {
            color: #0077b5;
            font-weight: bold;
            margin-right: 8px;
        }
        
        /* 内容区域 */
        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #0077b5;
        }
        
        .section h2 {
            color: #0077b5;
            font-size: 28px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e3f2fd;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #333;
            font-size: 22px;
            margin: 25px 0 15px 0;
            font-weight: bold;
        }
        
        .section h4 {
            color: #0077b5;
            font-size: 18px;
            margin: 20px 0 10px 0;
            font-weight: bold;
        }
        
        .section p {
            text-align: justify;
            margin-bottom: 15px;
            line-height: 1.8;
        }
        
        .section ul, .section ol {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .section li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .highlight h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        /* 进度条 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, #0077b5, #00a0dc);
            z-index: 9999;
            transition: width 0.3s ease;
        }
        
        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #0077b5;
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s;
            opacity: 0;
            transform: translateY(20px);
        }
        
        .back-to-top.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .back-to-top:hover {
            background: #005885;
            transform: translateY(-5px);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                position: fixed;
                top: 80px;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 200;
                background: white;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                overflow-y: auto;
                border-right: none;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }
            
            .sidebar.mobile-visible {
                transform: translateX(0);
            }
            
            .content {
                padding: 20px 15px;
                width: 100%;
            }
            
            .nav-toggle {
                display: block;
                z-index: 300;
            }
            
            .nav-search {
                margin: 0 10px;
                max-width: 150px;
            }
            
            .nav-tools {
                display: none;
            }
            
            .nav-tools.mobile-visible {
                display: flex;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #f8f9fa;
                padding: 15px;
                border-top: 1px solid #e9ecef;
                flex-direction: column;
                gap: 10px;
                z-index: 150;
            }
            
            .section {
                padding: 20px 15px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
            
            /* 遮罩层 */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 150;
            }
            
            .sidebar-overlay.visible {
                display: block;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 30px 15px;
            }
            
            .nav-bar {
                padding: 10px 15px;
            }
            
            .content {
                padding: 15px 10px;
            }
            
            .section {
                padding: 15px 10px;
            }
            
            .section h2 {
                font-size: 24px;
            }
            
            .section h3 {
                font-size: 20px;
            }
            
            .section h4 {
                font-size: 16px;
            }
        }
        
        /* 打印样式 */
        @media print {
            .nav-bar, .sidebar, .back-to-top, .progress-bar {
                display: none !important;
            }
            
            .main-content {
                flex-direction: column;
            }
            
            .content {
                padding: 0;
            }
            
            .section {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ddd;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar"></div>
    
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="confidential">内部资料 注意保密</div>
            <div class="year">2025 年度 LinkedIn 新升级</div>
            <h1 class="main-title">询盘倍增器：领英外贸高手进阶</h1>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <button class="nav-toggle" onclick="toggleSidebar()">📚 目录</button>
            <div class="nav-search">
                <input type="text" id="searchInput" placeholder="搜索内容..." onkeyup="searchContent()">
            </div>
            <div class="nav-tools" id="navTools">
                <button class="nav-btn" onclick="toggleNightMode()">🌙 夜间</button>
                <button class="nav-btn" onclick="window.print()">🖨️ 打印</button>
                <button class="nav-btn" onclick="toggleFontSize()">🔤 字体</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 侧边栏目录 -->
            <div class="sidebar" id="sidebar">
                <div class="toc">
                    <h3>📚 目录导航</h3>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" id="content">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">↑</button>
    
    <!-- 移动端遮罩层 -->
    <div class="sidebar-overlay" onclick="toggleSidebar()"></div>

    <script>
        // 全局变量
        let isNightMode = false;
        let fontSize = 16;
        
        // 原始文件内容
        const bookContent = {
            preface: `前言

在今天的全球化商业环境中，LinkedIn 已经成为外贸行业不可忽视的重要工具。从最初的社交平台，到如今的跨境商务桥梁，LinkedIn 不仅为外贸人提供了一个展示自我和产品的窗口，更为全球买家和卖家提供了一个高效、精准的交流平台。然而，如何在这个平台上脱颖而出、获取有效询盘，并将潜在客户转化为长期合作伙伴，仍然是许多外贸从业者面临的挑战。

本书《询盘倍增器：领英外贸高手进阶》，全面解析如何利用 LinkedIn 这一平台进行精准客户开发，从而提升询盘转化率、扩大国际市场份额。书中的每一节课都经过精心设计，涵盖了从账号优化、内容营销、客户开发，到高级工具使用、市场调研等各个方面的实战技巧。通过逐步推进的学习，您将能够深入了解如何借助 LinkedIn 的功能与策略，将线上交流转化为实际的业务成果。

不论您是刚刚踏入外贸行业的新手，还是已经拥有一定经验的外贸专家，本书都能为您提供可操作的策略与技巧，帮助您在激烈的市场竞争中占得先机。随着每一课的深入，您将发现，LinkedIn 不仅是一个社交工具，它更是您通向成功外贸事业的强大引擎。`,

            '1-1': `1.1 LinkedIn 的基础功能与外贸潜力

在全球化加速的背景下，LinkedIn 不仅是职业社交的中心舞台，更成为外贸行业掘金的新阵地。从个人品牌的塑造到精准客户的开发，从专业影响力的构建到数据驱动的运营优化，LinkedIn 所蕴含的功能与潜力值得每一位外贸人深入挖掘。

个人主页：信任构建的起点

LinkedIn 个人主页是外贸人展示专业形象的核心载体，其结构与内容决定了第一印象的质量。在外贸的语境中，个人主页不仅仅是职业背景的展示，更是对目标客户传递信任与价值的窗口。这一功能的潜力在于，它能够通过专业化、个性化的内容编排，直接传递"你能解决客户什么问题"的关键信息。

标题、简介、背景图片，这些看似简单的元素，却能深刻影响潜在客户的决策。尤其在外贸行业，客户对合作伙伴的第一印象往往决定了其后续的沟通意愿。个人主页在无声中传递了专业素养、行业资源与服务能力的复合信号，使客户在信息碎片化的环境中迅速对你形成清晰认知。

搜索功能：连接全球的桥梁

LinkedIn 的搜索功能在外贸领域拥有不可忽视的价值。它不仅是连接全球商业资源的技术支撑，更是精细化客户开发的关键环节。在外贸市场中，客户分布跨越多个国家、地区与行业，传统的线下展会、第三方代理模式逐渐被数字化工具所替代。而 LinkedIn 的搜索功能通过关键词与筛选条件的多维组合，使外贸人能够在全球范围内精准定位潜在客户。

这一功能的意义不仅在于找到客户，更在于通过数据化手段洞察市场趋势，优化客户画像。外贸人通过 LinkedIn 的搜索能力，可以构建出一个涵盖目标客户的动态资源池。这种资源池不仅是订单开发的基础，也是行业竞争力的重要体现。

内容发布：专业赋能与影响力传播

LinkedIn 的内容发布功能为外贸人提供了从信息提供者转型为行业领袖的可能性。在客户开发的过程中，传统营销手段面临着日益激烈的竞争，而专业化、场景化的内容输出则能够有效降低沟通门槛，建立信任关系。

在外贸环境中，内容发布的潜力体现在其长尾效应。高质量内容的传播不仅能够吸引直接受众，还能通过二级、三级传播扩大影响范围。尤其是行业洞察、案例分析与客户教育类内容，它们通过解决潜在客户的痛点、传递行业价值观而逐步强化品牌认知。此外，LinkedIn 的算法倾向于推广具有互动性的内容，外贸人通过精心设计的内容策略，可以最大化平台分发流量的利用效率。

互动与沟通：人性化连接的艺术

LinkedIn 的核心价值在于其所提供的高效互动机制。与传统的电子邮件或电话沟通相比，LinkedIn 的社交属性使其在客户开发中具备更强的情感温度与人性化特征。在外贸行业，客户沟通往往因文化差异与地域距离而显得疏离，而 LinkedIn 的互动功能则为拉近双方关系提供了天然的场景优势。

这种互动不仅限于直接对话，更包括点赞、评论以及动态的实时互动。外贸人可以通过对客户发布内容的深度参与，逐步建立认同感与专业影响力。LinkedIn 的社交属性赋予了沟通更多非正式化的可能，使其在传统客户开发模式中显得更加灵活与高效。

数据分析：战略决策的依据

LinkedIn 的数据分析功能是外贸人提升运营效率的关键抓手。在信息高度数字化的今天，决策的科学性与精准性依赖于数据的深度挖掘与解读。LinkedIn 通过对内容表现、受众特征与行为轨迹的全面统计，为外贸人提供了清晰的运营反馈与优化方向。

在外贸场景下，数据分析的价值不仅体现在过程改进上，更在于战略布局的优化。通过对客户群体的地域分布、行业偏好以及行为模式的分析，外贸人能够提前捕捉市场需求变化，调整资源分配与业务重心。同时，数据分析还能够帮助外贸人评估内容策略的有效性，为后续内容设计提供指导，从而实现资源利用的最大化。

躺赚潜力：重塑外贸竞争格局

LinkedIn 的基础功能看似简单，但其背后蕴含的潜力正在重塑外贸行业的竞争格局。从资源整合到精准营销，从信任构建到数据驱动，LinkedIn 以其高度专业化的功能为外贸人提供了跨越传统边界的全新工具。它不仅是一种平台，更是一种全新的商业逻辑，一种以专业为核心、以技术为依托的全球化商业实践方式。`,

            '1-2': `1.2 账号注册与职业定位

LinkedIn 作为职业社交平台的先驱，为外贸行业的从业者提供了突破传统模式的绝佳机会。然而，要充分发挥这一平台的潜力，外贸人必须以清晰的职业定位为核心，构建一个能够精准传递价值的专业账户。

在 LinkedIn 的生态中，注册账号的过程远不只是填写个人信息的简单步骤，而是一场对自我定位与职业规划的系统梳理。账号注册的每一环节都直接决定了你如何被潜在客户、合作伙伴甚至竞争对手感知。这不仅是一种形式上的呈现，更是一种价值与信任的塑造过程。

LinkedIn 账号注册：身份的数字化投射

在 LinkedIn 的语境中，账号本身是外贸人专业身份的数字化体现。它不仅仅是一种存在的证明，更是一种影响力的开始。在外贸行业，客户的信任往往来自对细节的关注。账号注册的过程中，每一项填写的内容，都在传递你对细节的理解与对专业性的追求。

从选择语言到设定基础信息，LinkedIn 的账号注册环节是一场精心设计的个人品牌打造过程。尤其对于外贸人而言，账号中的每一项内容不仅是对职业生涯的总结，更是对未来合作的承诺。它在无形中构建了一种预期——客户期待从你的账号中看到清晰的行业定位、丰富的国际经验以及对问题的独特洞察。

职业定位：价值主张的核心表达

外贸人的 LinkedIn 职业定位是账号的灵魂。它贯穿于个人简介、职位描述乃至动态发布的方方面面，决定了平台中其他用户如何解读你的角色与能力。在职业社交的语境中，定位的模糊往往意味着价值的稀释。

一个清晰且具有针对性的职业定位能够帮助外贸人高效连接目标受众。对于从事建材、化工、电子等外贸领域的专业人士，职业定位不仅需要反映当前的专业背景，更应体现对目标客户需求的深刻理解。这种定位不是单纯的自我描述，而是通过客户视角重新定义自己的价值主张。

个人品牌与行业话语权的初步构建

LinkedIn 的独特之处在于，它不仅仅是一个展示过往成就的平台，更是一个塑造未来职业方向的舞台。在外贸行业中，职业定位的背后隐含着一套关于个人品牌的长期战略。

个人品牌的核心在于建立行业话语权，而这需要通过一系列一致性与连贯性的表达来实现。从账号的基本信息到后续的内容发布，外贸人必须始终围绕特定主题展开，确保每一次互动，每一篇文章都在强化专业认知。

这种品牌塑造并非一蹴而就，而是通过反复的信息传递与价值展现逐步建立。例如，一名从事硅酮密封剂出口业务的外贸人，其职业定位可以围绕"为全球建筑行业提供高性能解决方案"展开。在此基础上，进一步通过与行业相关的关键词布局与资源展示，奠定专业权威形象。

细节决定成败：形象与信息的一致性

外贸行业的 LinkedIn 用户往往需要面对不同国家、文化背景的客户，因此职业定位在语言表达、文化适配性上的一致性尤为重要。这种一致性不仅体现在内容本身，还包括视觉形象的统一。

一个专业的头像、与行业相关的背景图片，以及简洁而富有感染力的个人标题，能够迅速传递信任与专业感。细节是 LinkedIn 职业定位的关键所在。它不仅影响潜在客户的第一印象，更直接关系到后续的互动与转化。

职业定位的意义在于帮助外贸人找到"被看见"的正确方式。在 LinkedIn 这个充满竞争的社交平台上，能够被精准识别、清晰记忆并持续关注，才是职业定位的最终目的。

职业定位的动态性与长期性

外贸人的职业定位并非一成不变，而是一个随行业趋势与个人发展不断演进的动态过程。在 LinkedIn 平台上，职业定位的变化应与行业需求同步，通过对市场与客户反馈的敏锐捕捉，持续优化自身的表达方式与内容结构。

这种动态性不仅体现了职业人的适应力与创新力，也为客户提供了不断更新的价值体验。而长期性的职业定位则帮助外贸人在竞争激烈的市场中，逐步积累品牌认知与行业影响力，最终将 LinkedIn 从一项工具转化为一项战略资产。

LinkedIn 账号注册与职业定位的本质是对外贸人专业性的系统化表达。这种表达既需要基于对行业的深入理解，也需要融入对个人价值的精准定义。只有通过对细节的不断雕琢与对长远目标的持续关注，外贸人才能在 LinkedIn 的职业社交生态中脱颖而出，为国际市场的竞争注入新的动力。`,

            '2-1': `2.1 LinkedIn 内容算法解析

在 LinkedIn 的社交生态中，内容的传播机制直接决定了账号的影响力与商业价值。对于外贸从业者而言，理解 LinkedIn 内容算法的逻辑不仅是平台运营的基础，更是实现业务目标的核心策略之一。LinkedIn 作为一款以职业社交为主的全球化平台，其内容算法的核心设计围绕"价值呈现"与"精准连接"展开。

LinkedIn 的内容算法不仅仅是一套数字计算的规则，更是一种动态机制，通过筛选、排序和推荐的过程，将优质内容传递给最合适的用户。其运作逻辑结合了用户行为数据、内容质量评估和网络拓展策略，形成了一个自适应的传播系统。

内容算法的核心逻辑：信任与专业为导向

在 LinkedIn 的职业生态中，信任是内容传播的核心要素，而专业性是评估内容价值的基础标准。算法通过对内容的深度分析，评估其与用户的兴趣和职业需求的契合度，从而决定内容的曝光范围。

用户行为驱动

LinkedIn 的算法高度依赖用户行为数据，包括点赞、评论、分享、关注等互动行为。这些数据帮助算法判断内容的受欢迎程度，并据此调整其在用户主页的展示优先级。对于外贸人来说，内容的互动性直接影响其在潜在客户群体中的传播广度

内容相关性

内容的相关性评估是 LinkedIn 算法的另一个关键指标。算法会结合用户的职业背景、行业领域、兴趣标签等信息，对内容的主题进行匹配分析。对于外贸人来说，创建与目标客户需求高度相关的内容是提升曝光的有效路径。

Linkedin 网络结构的加权机制

LinkedIn 的社交网络具有明显的分层特性，分为直接连接（1 度人脉）、间接连接（2 度人脉）和广泛网络（3 度及以上人脉）。算法在推荐内容时，会优先考虑 1 度和 2 度人脉的互动倾向，以确保内容传播的精准性与可信度。

内容质量的算法评估标准

LinkedIn 的内容算法不仅关注用户行为，还会对内容本身进行质量评估。这种评估不仅涉及内容的形式，还涵盖其深度、价值和传播潜力。

1.格式与可读性

内容的结构化表达是 LinkedIn 算法关注的重点。高质量的内容通常具有清晰的标题、简洁的段落和视觉辅助元素（如图片或视频）。对于外贸人来说，内容是否能快速传递核心信息至关重要，因为这直接影响潜在客户的阅读意愿。

2.互动性与分享潜力

互动性是算法评估内容传播价值的核心指标之一。点赞、评论和分享不仅表明内容的吸引力，也为算法提供了进一步推荐的依据。外贸人在内容创作中需要注重引导互动，以最大化其算法认可度。

3.内容的原创性与专业性

LinkedIn 强调原创内容的价值。与简单转发或复制的内容相比，原创内容更容易获得算法的青睐。对于外贸人来说，通过分享独到的行业见解、市场趋势分析或客户成功案例，可以有效提升内容的专业性评分。

算法推荐机制的动态特性

LinkedIn 的内容推荐算法并非一成不变，而是一个动态的、实时优化的系统。它会根据用户行为的变化，持续调整内容的推荐策略。

1.早期反馈机制

当内容发布后，算法会在短时间内监控其早期互动数据。如果内容在发布后迅速获得点赞或评论，算法会进一步扩大其推荐范围。这种机制为外贸人提供了利用"热点时机"发布内容的策略依据。

2.内容生命周期管理

内容的生命周期是 LinkedIn 算法评估的另一重要维度。高质量内容通常在发布时间后数天内持续获得曝光，而低质量内容则会被快速降权。因此，外贸人在运营过程中需要注重内容的持续优化与多次分发，以延长其生命周期。

3.网络权重与影响力传播

LinkedIn 的算法在内容传播过程中，还会考虑作者的网络权重，即作者在平台上的活跃度、专业度以及人脉圈层的影响力。对于外贸人来说，构建一个高质量的人脉网络有助于增强内容的初始传播力，从而提升整体曝光效果。

算法与商业目标的平衡

LinkedIn 内容算法的最终目标是帮助用户实现职业价值的最大化，而外贸人的商业目标通常围绕客户开发与品牌传播展开。在这种双重目标的驱动下，理解并利用算法成为外贸人获取竞争优势的重要手段。

1.内容频率与节奏的优化

算法偏好具有规律性发布的内容。对于外贸人来说，通过建立稳定的内容发布节奏，不仅可以满足算法的推荐逻辑，还能为客户传递稳定的价值感知。

2.行业热点的及时响应

LinkedIn 的算法对于与当前热点相关的内容具有优先推荐的倾向。因此，外贸人可以通过关注行业新闻与市场动态，快速创建热点相关的内容，以抢占算法推荐的先机。

3.潜在外贸客户参与度的提升

潜在客户的互动不仅是内容传播的驱动因素，也是算法评估内容价值的重要参考。通过设计互动性强的内容，例如行业问答、客户反馈调研或案例讨论，外贸人可以进一步提升客户参与度，从而放大内容的传播效果。

LinkedIn 的内容算法是平台生态的核心逻辑，也是外贸人成功运营的关键工具。理解算法的运作机制不仅有助于提升内容的曝光度，还能为外贸人制定更具针对性的内容策略提供依据。在算法与职业目标的交汇点上，外贸人可以通过高质量、相关性强的内容创作，与潜在客户建立更深层次的连接，最终实现品牌价值的最大化与业务增长的突破。`,

            '2-2': `2.2 撰写高互动率的帖子：选题与内容节奏

在 LinkedIn 平台的职业社交生态中，内容是连接产品与外贸客户的桥梁。对于外贸运营而言，撰写高互动率的帖子不仅是吸引客户和合作伙伴的有效途径，更是建立信任与影响力的重要手段。在外贸环境下，内容的选题与发布节奏直接关系到信息的传播广度和品牌的持久影响力。

选题：客户需求与行业趋势的交汇点

高互动率的帖子源于深刻理解受众的需求与兴趣。在外贸领域，选题不仅是表达个人见解的出口，更是链接行业洞察与客户痛点的关键环节。有效的选题必须具备以下几个核心特征：

1. 符合目标受众的需求画像

外贸人的 LinkedIn 受众通常包括潜在客户、行业同仁、供应链合作伙伴以及其他相关利益方。因此，选题的首要任务是从受众视角出发，捕捉其关心的核心问题。对于潜在客户来说，他们可能关注的是市场趋势、产品性能或成本优化。而对于同行业者，则可能更多关注行业资讯、贸易政策的变化或技术创新的动态。

2. 与外贸运营的日常实践深度契合

内容选题需要与外贸业务成交订单的核心流程密切相关，例如供应链优化、物流管理、海外市场的机会分析等。这样的内容不仅有助于展现专业性，还能够吸引目标受众的持续关注。

例如，讨论如何应对国际物流成本上涨，或者分析某一市场的进出口政策变化，这些选题不仅具有实用价值，还能够促使受众参与讨论，提升内容的互动率。

3. 体现行业趋势与全球视角

高互动率的内容通常能够回应行业的热点话题或趋势。外贸人可以结合最新的市场动态、全球供应链的变化，或特定产品类别的需求波动，创造具有时效性的选题。这不仅能够展现个人或企业的敏锐洞察力，还可以增强内容的传播性与互动性。

例如，在疫情对国际贸易造成深远影响的背景下，分析疫情后某个地区的供应链复苏情况，会引发广泛的关注与讨论。

节奏：持续输出与高效连接的平衡

在 LinkedIn 上，内容发布的节奏直接影响用户的阅读习惯与粘性。在外贸运营中，撰写高互动率帖子的节奏规划需要兼顾发布频率、主题深度与受众期待，确保内容的吸引力与持续性。

1. 节奏的意义：内容策略的长线规划

内容节奏不仅是时间上的安排，更是一种策略性的布局。在外贸行业，客户的关注点具有阶段性变化，例如年初关注预算与规划，中期关注执行情况，年末则关注回顾与总结。因此，内容节奏需要围绕这些关键节点，形成与客户需求同步的输出频率。

这种节奏的设计能够帮助外贸人占据受众的心智，在关键时间点提高内容的曝光率与互动性。

2. 确保多样性与连贯性

高互动率的内容需要在形式与主题上保持多样性。例如，可以在一周内安排不同类型的内容发布：某天分享行业洞察，某天发布客户案例或成功经验，另一时间则专注于回应观众的提问或提供实用技巧。这种多样化的节奏能够维持受众的兴趣，同时通过连贯的主题表达强化个人品牌。

3. 周期性与互动窗口的优化

在 LinkedIn 的平台生态中，内容的生命力通常与互动的时间窗口密切相关。外贸人需要根据受众所在时区与行业作息习惯，选择最佳的发布时间。例如，欧美市场的受众通常在工作日的早晨或午休时段浏览内容，而亚洲市场则可能更偏好傍晚或周末的时间段。

通过数据分析与反馈监控，外贸人可以优化内容发布的周期性与时间点，从而提升帖子的曝光率与互动率。

选题与节奏的协同作用：从内容到影响力的延伸

在 LinkedIn 平台上，选题与节奏并非孤立的概念，而是构建高互动率内容的双轮驱动。有效的选题确保了内容的价值与吸引力，而合理的节奏则放大了内容的传播效果与长期影响力。

例如，一篇关于供应链优化的深度分析文章，若搭配后续的系列内容（如客户案例分享、工具推荐或与观众的互动问答），不仅能够延长该主题的生命周期，还能够通过持续输出累积更多的关注与互动。

这种协同作用的核心在于，让选题的深度与节奏的连贯性形成合力，推动内容从单一表达走向持续影响，最终为外贸运营的 LinkedIn 账号打造更强的品牌形象与市场吸引力。

撰写高互动率的帖子，是一项需要系统化思维与精细化执行的内容策略。在外贸运营中，选题与节奏的设计必须紧密围绕目标受众的需求与行业特性，通过多维度的内容输出实现更广泛的影响力与互动性。这不仅是 LinkedIn 账号日常运营的关键，更是外贸人在职业社交平台中占据核心位置的重要手段。

在全球化竞争日益激烈的背景下，内容的选题与节奏规划不仅仅是技术层面的实践，更是一种传递价值与塑造品牌的长期策略。外贸人唯有在这一过程中不断优化自身的表达与洞察，方能在 LinkedIn 的广阔舞台上赢得更多关注与认可。`,

            '3-1': `3.1 精准定位目标客户的搜索技巧

在外贸行业中，LinkedIn 不仅是一个展示个人或公司形象的社交平台，更是与全球潜在客户建立联系、寻找商业机会的关键渠道。精准定位目标客户的能力，直接影响到外贸业务的拓展与成功。在 LinkedIn 上，如何有效地进行客户搜索，如何利用平台提供的各种功能和技巧精准锁定潜在合作伙伴，是外贸运营人员必须掌握的核心能力之一。

精准的客户定位不仅仅依赖于平台的搜索工具，更需要外贸人具备深刻的行业洞察力、市场敏感性以及对客户需求的准确把握。要做到这一点，必须将平台的技术优势与自身的业务目标结合，合理运用各种筛选工具，通过多维度的信息整合和分析，找到与自己产品或服务高度契合的目标客户群体。

客户定位的核心概念与前提条件

在 LinkedIn 上进行客户搜索并不是单纯的"找到某人"，而是要找到那些在某一特定领域、特定时间和特定需求下，最有可能成为合作伙伴的人或公司。因此，精确的客户定位是外贸成功的第一步。精准定位的前提是对目标市场、客户画像以及行业趋势的深刻理解。

1. 目标市场的选择与细化

外贸行业涉及的市场广泛且多样，不同的区域、不同的行业甚至不同的需求层次，都可能影响客户的选择。因此，外贸人员首先需要通过行业研究、市场调研和竞争分析等手段，明确自己的目标市场，并根据市场的不同特征细化客户群体。

例如，对于专注于建筑材料出口的外贸人来说，其目标市场可能是正在进行基础设施建设的国家或地区。这一市场细分的明确，帮助外贸人更加聚焦于符合条件的客户群体，从而提高搜索效率，避免无效信息的干扰。

2. 客户画像的精准描绘

在 LinkedIn 上进行客户搜索时，定义清晰的客户画像至关重要。客户画像不仅包括行业、职位、公司规模等基本信息，还应涵盖客户的需求、痛点以及购买决策的周期等因素。客户画像的精准度决定了搜索结果的相关性与有效性。

例如，一个主要针对大型建筑公司提供硅酮密封剂的外贸商，其客户画像可能包括：
• 行业：建筑、施工、材料供应
• 职位：采购经理、工程师、供应链负责人
• 公司规模：年营业额超过 5000 万美元的大型公司
• 需求痛点：寻求高品质、低成本的建筑密封材料，且要求长期供应

LinkedIn 高效搜索技巧：精准筛选潜在客户

LinkedIn 提供了强大的搜索功能，可以帮助外贸人员基于不同的筛选条件，精准定位潜在客户。掌握这些搜索技巧，可以大大提高客户寻找的效率与准确性。

1. 高级搜索与过滤器的合理使用

LinkedIn 提供了多种搜索过滤器，外贸人员可以根据行业、地区、职位、公司规模等维度，精确定位潜在客户。例如，使用"行业"和"职位"两个过滤器，可以帮助外贸人员快速筛选出在建筑、制造或化工等行业工作的采购经理或工程师。

除了这些基础的过滤条件外，LinkedIn 还提供了如"公司规模""语言能力"等高级选项，可以进一步缩小搜索范围，确保搜索结果与目标客户群体的需求高度契合。

2. 关键词搜索的巧妙运用

在 LinkedIn 的搜索框中，外贸人员可以输入精准的关键词，这些关键词通常与客户的需求、职位或行业相关。通过组合使用多个关键词（例如，建筑材料、采购经理、密封剂等），可以提高搜索的准确度。

关键词的选择要避免过于宽泛，如"采购"这一关键词可能涉及到各行各业。精准的关键词应紧密围绕目标客户的实际需求，例如，"建筑项目采购经理"或"建筑材料供应商"等。利用这些具体的关键词，外贸人员能够迅速锁定符合条件的潜在客户。

3. 使用 Boolean 运算符进行精确匹配

LinkedIn 支持 Boolean 搜索，这意味着可以通过组合使用 AND、OR、NOT 等运算符，对搜索结果进行更加精细的筛选。运用 Boolean 搜索技巧，外贸人员可以根据需求灵活调整搜索逻辑，缩小搜索范围，提高搜索结果的相关性。

例如，使用"AND"运算符可以在同一搜索中同时定位到"建筑材料"与"采购经理"的客户群体，而使用"NOT"可以排除不相关的行业或职位。例如，搜索"建筑材料 AND 采购经理 NOT 销售经理"将排除所有与销售相关的结果，聚焦于采购决策者。

4. 精细化地域定位

外贸客户往往具有明显的地理分布特征，LinkedIn 提供了区域搜索功能，外贸人员可以根据不同的市场选择目标国家或地区。通过定位具体的地理区域，外贸人员不仅可以缩小搜索范围，还能更好地针对当地市场的特性和客户需求，进行个性化的沟通与营销。

例如，如果外贸人员专注于东南亚市场，可以通过选择具体的国家（如印度尼西亚、泰国、马来西亚等）进行搜索，迅速锁定该地区的潜在客户。地域搜索的精细化，使得客户定位更为高效与有针对性。

数据分析与客户行为的洞察

精准定位目标客户不仅仅依赖于搜索工具，数据分析和客户行为洞察同样至关重要。LinkedIn 提供了丰富的客户活动数据，如个人动态、职位更新、参与的讨论等，这些数据为外贸人员提供了宝贵的客户需求信号。

外贸人员可以通过分析潜在客户的动态，了解他们近期的关注重点与业务需求，从而进一步调整搜索策略与沟通内容。例如，若某客户频繁发布关于建筑项目采购的动态，表明其对建筑材料的需求可能正在增加。此时，外贸人员可以适时向该客户推荐相关产品或服务，提高客户转化率。

在 LinkedIn 上，精准定位目标客户是一项综合性技能，涉及市场分析、客户画像绘制、搜索技巧的熟练运用以及数据洞察的能力。通过有效利用 LinkedIn 提供的搜索工具与策略，外贸人员可以大幅提高找到潜在客户的效率，并建立起有针对性的客户关系。`
        };

        // 目录结构
        const tableOfContents = [
            {
                title: "第一部分：LinkedIn 基础与账号优化",
                chapters: [
                    { id: "1-1", title: "LinkedIn 的基础功能与外贸潜力" },
                    { id: "1-2", title: "账号注册与职业定位" },
                    { id: "1-3", title: "如何选择高效的个人主页照片和封面" },
                    { id: "1-4", title: "优化个人简介：突出行业关键词" },
                    { id: "1-5", title: "完善工作经历和项目经验" },
                    { id: "1-6", title: "技能标签与证书展示的设置技巧" },
                    { id: "1-7", title: "如何撰写吸引客户的个人概要" },
                    { id: "1-8", title: "公司主页的搭建与品牌形象优化" },
                    { id: "1-9", title: "掌握 LinkedIn 搜索功能：精准找到潜在客户" },
                    { id: "1-10", title: "使用 LinkedIn Premium 和 Sales Navigator 的优劣分析" }
                ]
            },
            {
                title: "第二部分：内容营销与品牌塑造",
                chapters: [
                    { id: "2-1", title: "LinkedIn 内容算法解析" },
                    { id: "2-2", title: "撰写高互动率的帖子：选题与节奏" },
                    { id: "2-3", title: "视频内容的制作与发布技巧" },
                    { id: "2-4", title: "行业趋势文章：如何以专家身份吸引客户" },
                    { id: "2-5", title: "如何策划月度内容主题并保持一致性" },
                    { id: "2-6", title: "用户评论和互动：建立信任感" },
                    { id: "2-7", title: "高效利用文档分享功能展示产品价值" },
                    { id: "2-8", title: "客户痛点分析与内容切入点选择" },
                    { id: "2-9", title: "长文与短文：不同形式的内容策略" },
                    { id: "2-10", title: "创建病毒式传播的内容技巧" },
                    { id: "2-11", title: "如何用热点新闻与话题提升曝光" },
                    { id: "2-22", title: "如何通过内容引导询盘" }
                ]
            },
            {
                title: "第三部分：客户开发与主动联系策略",
                chapters: [
                    { id: "3-1", title: "精准定位目标客户的搜索技巧" },
                    { id: "3-2", title: "添加客户的个性化连接请求方法" },
                    { id: "3-3", title: "初次接触客户的高效开场白" },
                    { id: "3-4", title: "如何在 LinkedIn 聊天中建立信任感" },
                    { id: "3-5", title: "客户跟进与 CRM 工具结合使用" },
                    { id: "3-6", title: "分析客户的行为：谁看了你的资料？" },
                    { id: "3-7", title: "如何处理客户的冷淡回应" },
                    { id: "3-8", title: "制作引流话术并引导客户到邮件沟通" },
                    { id: "3-9", title: "针对不同文化背景的沟通技巧" },
                    { id: "3-10", title: "高效解决客户的常见顾虑" },
                    { id: "3-11", title: "从沟通到下单：如何推动客户成交" }
                ]
            },
            {
                title: "第四部分：专业领域展示与社交信任建立",
                chapters: [
                    { id: "4-1", title: "如何加入行业群组并建立影响力" },
                    { id: "4-2", title: "在群组中分享有价值的内容" },
                    { id: "4-3", title: "通过 LinkedIn 活动页面推广产品或服务" },
                    { id: "4-4", title: "推荐信和客户评价的重要性及获取方法" },
                    { id: "4-5", title: "如何利用共同联系打造专业信任度" },
                    { id: "4-6", title: "与潜在客户共同创建文章的方法" },
                    { id: "4-7", title: "利用 LinkedIn Pulse 发布专业行业文章" },
                    { id: "4-8", title: "定期更新并优化个人主页以保持专业形象" },
                    { id: "4-9", title: "客户成功案例展示与影响力扩大" },
                    { id: "4-10", title: "从"个人品牌"到"行业领袖"的升级路线" },
                    { id: "4-11", title: "如何成为 LinkedIn 的 Top Voice" }
                ]
            },
            {
                title: "第五部分：竞争对手与市场调研",
                chapters: [
                    { id: "5-1", title: "如何监控竞争对手的 LinkedIn 活动" },
                    { id: "5-2", title: "发现市场趋势并快速调整策略" },
                    { id: "5-3", title: "使用 LinkedIn 数据分析目标市场" },
                    { id: "5-4", title: "评估竞争对手的内容策略" },
                    { id: "5-5", title: "如何借鉴竞争对手的优质案例" },
                    { id: "5-6", title: "开展市场问卷调查并吸引客户参与" },
                    { id: "5-7", title: "通过客户的职业轨迹分析需求变化" },
                    { id: "5-8", title: "收集客户反馈优化服务流程" },
                    { id: "5-9", title: "如何定位高潜力市场" },
                    { id: "5-10", title: "评估市场机会与风险" }
                ]
            },
            {
                title: "第六部分：提升互动与粉丝增长",
                chapters: [
                    { id: "6-1", title: "如何鼓励客户参与内容互动" },
                    { id: "6-2", title: "组织有奖活动增加粉丝量" },
                    { id: "6-3", title: "高效利用 LinkedIn 广告推广内容" },
                    { id: "6-4", title: "增加个人资料浏览量的小技巧" },
                    { id: "6-5", title: "借助话题标签吸引更多关注" },
                    { id: "6-6", title: "提高内容互动率的具体策略" },
                    { id: "6-7", title: "如何策划季度增长计划" },
                    { id: "6-8", title: "制作潜在客户的"引流工具"" },
                    { id: "6-9", title: "推动客户主动向同事或朋友推荐" },
                    { id: "6-10", title: "保持增长势头的持续优化" },
                    { id: "6-11", title: "如何转化粉丝为真实客户" }
                ]
            },
            {
                title: "第七部分：高级工具与实用技巧",
                chapters: [
                    { id: "7-1", title: "使用 LinkedIn Analytics 优化表现" },
                    { id: "7-2", title: "如何导出客户数据并跟进" },
                    { id: "7-3", title: "借助外部工具批量处理客户信息" },
                    { id: "7-4", title: "自动化营销工具在 LinkedIn 的使用" },
                    { id: "7-5", title: "用 InMail 提升联系效率" },
                    { id: "7-6", title: "多账号协作开发潜在客户" },
                    { id: "7-7", title: "客户数据的整理与分级管理" },
                    { id: "7-8", title: "结合其他平台如 WhatsApp 的高效引流" },
                    { id: "7-9", title: "利用 LinkedIn API 整合客户资源" }
                ]
            },
            {
                title: "第八部分：案例解析与实战演练",
                chapters: [
                    { id: "8-1", title: "成功获取百万订单的案例分享" },
                    { id: "8-2", title: "分析失败案例的教训与改进" },
                    { id: "8-3", title: "实战演练：设计自己的 LinkedIn 外贸开发计划" }
                ]
            }
        ];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadOriginalContent();
            generateTableOfContents();
            updateProgressBar();
            updateBackToTopButton();
        });

        // 滚动事件监听
        window.addEventListener('scroll', function() {
            updateProgressBar();
            updateBackToTopButton();
        });

        // 加载原始内容
        function loadOriginalContent() {
            const content = document.getElementById('content');

            // 添加前言
            content.innerHTML = `
                <div class="section" id="preface">
                    <h2>前言</h2>
                    <div class="content-text">
                        ${bookContent.preface.split('\n\n').map(p => `<p>${p}</p>`).join('')}
                    </div>
                </div>
            `;

            // 添加已有的章节内容
            Object.keys(bookContent).forEach(key => {
                if (key !== 'preface') {
                    const chapterContent = bookContent[key];
                    const lines = chapterContent.split('\n\n');
                    const title = lines[0];
                    const contentText = lines.slice(1).join('\n\n');

                    content.innerHTML += `
                        <div class="section" id="section-${key}">
                            <h3>${title}</h3>
                            <div class="content-text">
                                ${contentText.split('\n\n').map(p => {
                                    if (p.trim().startsWith('个人主页：') || p.trim().startsWith('搜索功能：') ||
                                        p.trim().startsWith('内容发布：') || p.trim().startsWith('互动与沟通：') ||
                                        p.trim().startsWith('数据分析：') || p.trim().startsWith('躺赚潜力：') ||
                                        p.trim().startsWith('LinkedIn 账号注册：') || p.trim().startsWith('职业定位：') ||
                                        p.trim().startsWith('个人品牌与行业话语权：') || p.trim().startsWith('细节决定成败：') ||
                                        p.trim().startsWith('职业定位的动态性：')) {
                                        return `<div class="highlight"><h4>${p}</h4></div>`;
                                    }
                                    return `<p>${p}</p>`;
                                }).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            // 添加占位符章节（显示"内容正在整理中"）
            tableOfContents.forEach(part => {
                part.chapters.forEach(chapter => {
                    if (!bookContent[chapter.id]) {
                        content.innerHTML += `
                            <div class="section" id="section-${chapter.id}">
                                <h3>${chapter.title}</h3>
                                <div class="content-text">
                                    <div class="highlight">
                                        <h4>📝 内容正在整理中</h4>
                                        <p>本章节内容正在从原始文档中提取和整理，请稍后查看。我们正在努力保持原始内容的完整性和准确性。</p>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });
            });
        }

        // 生成目录
        function generateTableOfContents() {
            const tocContainer = document.querySelector('.toc');

            tableOfContents.forEach(part => {
                const partElement = document.createElement('div');
                partElement.className = 'part';

                const partTitle = document.createElement('div');
                partTitle.className = 'part-title';
                partTitle.textContent = part.title;
                partTitle.onclick = () => togglePart(partElement);

                const chaptersContainer = document.createElement('div');
                chaptersContainer.className = 'chapters';

                part.chapters.forEach(chapter => {
                    const chapterElement = document.createElement('div');
                    chapterElement.className = 'chapter';
                    chapterElement.innerHTML = `
                        <span class="chapter-number">${chapter.id}</span>
                        ${chapter.title}
                    `;
                    chapterElement.onclick = () => scrollToSection(chapter.id);
                    chaptersContainer.appendChild(chapterElement);
                });

                partElement.appendChild(partTitle);
                partElement.appendChild(chaptersContainer);
                tocContainer.appendChild(partElement);
            });

            // 添加前言链接
            const prefaceElement = document.createElement('div');
            prefaceElement.className = 'chapter';
            prefaceElement.innerHTML = `
                <span class="chapter-number">📖</span>
                前言
            `;
            prefaceElement.onclick = () => scrollToSection('preface');
            tocContainer.insertBefore(prefaceElement, tocContainer.firstChild);
        }

        // 切换部分展开/收起
        function togglePart(partElement) {
            const chapters = partElement.querySelector('.chapters');
            chapters.classList.toggle('expanded');
        }

        // 滚动到指定章节
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId) || document.getElementById(`section-${sectionId}`);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    toggleSidebar();
                }
            }
        }

        // 切换侧边栏（移动端）
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            sidebar.classList.toggle('mobile-visible');
            overlay.classList.toggle('visible');

            // 防止背景滚动
            if (sidebar.classList.contains('mobile-visible')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        // 切换夜间模式
        function toggleNightMode() {
            isNightMode = !isNightMode;
            const body = document.body;
            const container = document.querySelector('.container');
            const sections = document.querySelectorAll('.section');
            const sidebar = document.querySelector('.sidebar');
            const navBar = document.querySelector('.nav-bar');
            const toc = document.querySelector('.toc');
            const parts = document.querySelectorAll('.part');
            const chapters = document.querySelectorAll('.chapter');
            const highlights = document.querySelectorAll('.highlight');
            const nightModeBtn = document.querySelector('.nav-btn');

            if (isNightMode) {
                // 夜间模式
                body.style.background = 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)';
                body.style.color = '#e0e0e0';

                container.style.background = '#2d2d2d';
                container.style.color = '#e0e0e0';

                sections.forEach(section => {
                    section.style.background = '#3a3a3a';
                    section.style.color = '#e0e0e0';
                    section.style.borderLeftColor = '#0099cc';
                });

                if (sidebar) {
                    sidebar.style.background = '#2d2d2d';
                    sidebar.style.color = '#e0e0e0';
                }

                if (navBar) {
                    navBar.style.background = '#2d2d2d';
                    navBar.style.color = '#e0e0e0';
                }

                highlights.forEach(highlight => {
                    highlight.style.background = 'linear-gradient(135deg, #4a4a00 0%, #666600 100%)';
                    highlight.style.color = '#fff';
                    highlight.style.borderLeftColor = '#ffcc00';
                });

                nightModeBtn.textContent = '☀️ 日间';
            } else {
                // 日间模式
                body.style.background = 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';
                body.style.color = '#333';

                container.style.background = 'white';
                container.style.color = '#333';

                sections.forEach(section => {
                    section.style.background = 'white';
                    section.style.color = '#333';
                    section.style.borderLeftColor = '#0077b5';
                });

                if (sidebar) {
                    sidebar.style.background = '#f8f9fa';
                    sidebar.style.color = '#333';
                }

                if (navBar) {
                    navBar.style.background = '#f8f9fa';
                    navBar.style.color = '#333';
                }

                highlights.forEach(highlight => {
                    highlight.style.background = 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)';
                    highlight.style.color = '#333';
                    highlight.style.borderLeftColor = '#ffc107';
                });

                nightModeBtn.textContent = '🌙 夜间';
            }
        }

        // 切换字体大小
        function toggleFontSize() {
            fontSize = fontSize === 16 ? 18 : fontSize === 18 ? 20 : 16;
            document.body.style.fontSize = fontSize + 'px';
        }

        // 搜索内容
        function searchContent() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sections = document.querySelectorAll('.section');

            sections.forEach(section => {
                const text = section.textContent.toLowerCase();
                if (searchTerm === '' || text.includes(searchTerm)) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            });
        }

        // 更新进度条
        function updateProgressBar() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const progress = (scrollTop / scrollHeight) * 100;
            document.querySelector('.progress-bar').style.width = progress + '%';
        }

        // 更新返回顶部按钮
        function updateBackToTopButton() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }

        // 返回顶部
        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
