#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装PDF转换所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def main():
    """主函数"""
    print("🔧 开始安装PDF转换所需的依赖包...")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "reportlab",  # PDF生成库
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        print(f"正在安装 {package}...")
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖包安装完成!")
        print("现在可以运行 python pdf_converter.py 来转换文档了")
    else:
        print("⚠️  部分包安装失败，请检查网络连接或手动安装")
        print("手动安装命令:")
        for package in packages:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
