#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档PDF转换工具
将文本文件转换为格式化的PDF文档
"""

import os
import sys
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
import re

class PDFConverter:
    def __init__(self):
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试注册中文字体
            font_paths = [
                "C:/Windows/Fonts/simsun.ttc",  # Windows 宋体
                "C:/Windows/Fonts/msyh.ttc",    # Windows 微软雅黑
                "/System/Library/Fonts/PingFang.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Chinese', font_path))
                        self.chinese_font = 'Chinese'
                        print(f"成功加载字体: {font_path}")
                        return
                    except:
                        continue
            
            # 如果没有找到中文字体，使用默认字体
            self.chinese_font = 'Helvetica'
            print("警告: 未找到中文字体，使用默认字体")
            
        except Exception as e:
            print(f"字体设置错误: {e}")
            self.chinese_font = 'Helvetica'
    
    def setup_styles(self):
        """设置文档样式"""
        self.styles = getSampleStyleSheet()
        
        # 标题样式
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Title'],
            fontName=self.chinese_font,
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor='#2C3E50'
        )
        
        # 章节标题样式
        self.chapter_style = ParagraphStyle(
            'ChapterTitle',
            parent=self.styles['Heading1'],
            fontName=self.chinese_font,
            fontSize=16,
            spaceAfter=15,
            spaceBefore=20,
            textColor='#34495E'
        )
        
        # 小节标题样式
        self.section_style = ParagraphStyle(
            'SectionTitle',
            parent=self.styles['Heading2'],
            fontName=self.chinese_font,
            fontSize=14,
            spaceAfter=10,
            spaceBefore=15,
            textColor='#5D6D7E'
        )
        
        # 正文样式
        self.body_style = ParagraphStyle(
            'CustomBody',
            parent=self.styles['Normal'],
            fontName=self.chinese_font,
            fontSize=11,
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            leftIndent=0,
            rightIndent=0
        )
        
        # 目录样式
        self.toc_style = ParagraphStyle(
            'TOC',
            parent=self.styles['Normal'],
            fontName=self.chinese_font,
            fontSize=12,
            spaceAfter=5,
            leftIndent=20
        )
    
    def parse_content(self, content):
        """解析文档内容"""
        lines = content.split('\n')
        parsed_content = []
        toc_items = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测标题层级
            if self.is_main_title(line):
                parsed_content.append(('title', line))
                toc_items.append((1, line))
            elif self.is_chapter_title(line):
                parsed_content.append(('chapter', line))
                toc_items.append((2, line))
            elif self.is_section_title(line):
                parsed_content.append(('section', line))
                toc_items.append((3, line))
            else:
                parsed_content.append(('body', line))
        
        return parsed_content, toc_items
    
    def is_main_title(self, line):
        """判断是否为主标题"""
        return '询盘倍增器' in line or '领英外贸高手进阶' in line
    
    def is_chapter_title(self, line):
        """判断是否为章节标题"""
        patterns = [
            r'^第[一二三四五六七八九十\d]+部分',
            r'^第[一二三四五六七八九十\d]+章',
            r'^\d+\.\d+\s',
            r'^[1-8]\.\d+\s'
        ]
        return any(re.match(pattern, line) for pattern in patterns)
    
    def is_section_title(self, line):
        """判断是否为小节标题"""
        patterns = [
            r'^\s*[1-9]\.\s',
            r'^\s*\d+\.\s',
            r'^[•·]\s',
            r'^\s*\([一二三四五六七八九十\d]+\)'
        ]
        return any(re.match(pattern, line) for pattern in patterns)
    
    def create_toc(self, toc_items):
        """创建目录"""
        toc_content = []
        toc_content.append(Paragraph("目录", self.chapter_style))
        toc_content.append(Spacer(1, 20))
        
        for level, title in toc_items:
            if level <= 2:  # 只显示主要章节
                indent = (level - 1) * 20
                toc_style = ParagraphStyle(
                    'TOCLevel',
                    parent=self.toc_style,
                    leftIndent=indent
                )
                toc_content.append(Paragraph(title, toc_style))
        
        toc_content.append(PageBreak())
        return toc_content
    
    def convert_to_pdf(self, input_file, output_file):
        """转换文本文件为PDF"""
        try:
            # 读取输入文件
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析内容
            parsed_content, toc_items = self.parse_content(content)
            
            # 创建PDF文档
            doc = SimpleDocTemplate(
                output_file,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=72
            )
            
            # 构建文档内容
            story = []
            
            # 添加目录
            story.extend(self.create_toc(toc_items))
            
            # 添加正文内容
            for content_type, text in parsed_content:
                if content_type == 'title':
                    story.append(Paragraph(text, self.title_style))
                    story.append(Spacer(1, 30))
                elif content_type == 'chapter':
                    story.append(Spacer(1, 20))
                    story.append(Paragraph(text, self.chapter_style))
                elif content_type == 'section':
                    story.append(Paragraph(text, self.section_style))
                elif content_type == 'body':
                    if text:  # 只添加非空内容
                        story.append(Paragraph(text, self.body_style))
            
            # 生成PDF
            doc.build(story)
            print(f"PDF文件已生成: {output_file}")
            return True
            
        except Exception as e:
            print(f"转换过程中出现错误: {e}")
            return False

def main():
    """主函数"""
    converter = PDFConverter()
    
    # 设置输入和输出文件
    input_file = "1"  # 输入文件名
    output_file = "LinkedIn外贸营销指南.pdf"  # 输出PDF文件名
    
    print("开始转换文档...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return
    
    # 执行转换
    success = converter.convert_to_pdf(input_file, output_file)
    
    if success:
        print("✅ 转换完成!")
        print(f"📄 PDF文件已保存为: {output_file}")
        
        # 显示文件信息
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"📊 文件大小: {file_size / 1024:.1f} KB")
    else:
        print("❌ 转换失败")

if __name__ == "__main__":
    main()
