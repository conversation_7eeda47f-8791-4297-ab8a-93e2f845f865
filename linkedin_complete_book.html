<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>询盘倍增器：领英外贸高手进阶</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.8;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-size: 16px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        
        /* 头部样式 */
        .header {
            background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .confidential {
            color: #ffeb3b;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .year {
            color: rgba(255,255,255,0.8);
            font-size: 16px;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .main-title {
            font-size: clamp(24px, 5vw, 36px);
            font-weight: bold;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }
        
        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 18px;
            font-style: italic;
            position: relative;
            z-index: 1;
        }
        
        /* 导航栏 */
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-toggle {
            display: none;
            background: #0077b5;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .nav-search {
            flex: 1;
            max-width: 300px;
            margin: 0 20px;
        }
        
        .nav-search input {
            width: 100%;
            padding: 8px 15px;
            border: 2px solid #0077b5;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .nav-tools {
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            background: #0077b5;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .nav-btn:hover {
            background: #005885;
        }
        
        /* 主要内容区域 */
        .main-content {
            display: flex;
            min-height: calc(100vh - 200px);
        }
        
        /* 侧边栏目录 */
        .sidebar {
            width: 350px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            position: sticky;
            top: 80px;
        }
        
        .toc {
            padding: 20px;
        }
        
        .toc h3 {
            color: #0077b5;
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #0077b5;
            padding-bottom: 10px;
        }
        
        .part {
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .part-title {
            background: #0077b5;
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .part-title:hover {
            background: #005885;
        }
        
        .chapters {
            background: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .chapters.expanded {
            max-height: 1000px;
        }
        
        .chapter {
            padding: 10px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
        }
        
        .chapter:hover {
            background: #e3f2fd;
            padding-left: 20px;
        }
        
        .chapter:last-child {
            border-bottom: none;
        }
        
        .chapter-number {
            color: #0077b5;
            font-weight: bold;
            margin-right: 8px;
        }
        
        /* 内容区域 */
        .content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #0077b5;
        }
        
        .section h2 {
            color: #0077b5;
            font-size: 28px;
            margin-bottom: 20px;
            border-bottom: 2px solid #e3f2fd;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #333;
            font-size: 22px;
            margin: 25px 0 15px 0;
            font-weight: bold;
        }
        
        .section h4 {
            color: #0077b5;
            font-size: 18px;
            margin: 20px 0 10px 0;
            font-weight: bold;
        }
        
        .section p {
            text-align: justify;
            margin-bottom: 15px;
            line-height: 1.8;
        }
        
        .section ul, .section ol {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .section li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .highlight h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        /* 进度条 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: linear-gradient(90deg, #0077b5, #00a0dc);
            z-index: 9999;
            transition: width 0.3s ease;
        }
        
        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #0077b5;
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s;
            opacity: 0;
            transform: translateY(20px);
        }
        
        .back-to-top.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .back-to-top:hover {
            background: #005885;
            transform: translateY(-5px);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                position: static;
                max-height: none;
                order: 2;
            }
            
            .content {
                padding: 20px 15px;
                order: 1;
            }
            
            .nav-toggle {
                display: block;
            }
            
            .nav-search {
                margin: 0 10px;
                max-width: 200px;
            }
            
            .nav-tools {
                display: none;
            }
            
            .nav-tools.mobile-visible {
                display: flex;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #f8f9fa;
                padding: 15px;
                border-top: 1px solid #e9ecef;
                flex-direction: column;
                gap: 10px;
            }
            
            .sidebar {
                display: none;
            }
            
            .sidebar.mobile-visible {
                display: block;
            }
            
            .section {
                padding: 20px 15px;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 30px 15px;
            }
            
            .nav-bar {
                padding: 10px 15px;
            }
            
            .content {
                padding: 15px 10px;
            }
            
            .section {
                padding: 15px 10px;
            }
            
            .section h2 {
                font-size: 24px;
            }
            
            .section h3 {
                font-size: 20px;
            }
            
            .section h4 {
                font-size: 16px;
            }
        }
        
        /* 打印样式 */
        @media print {
            .nav-bar, .sidebar, .back-to-top, .progress-bar {
                display: none !important;
            }
            
            .main-content {
                flex-direction: column;
            }
            
            .content {
                padding: 0;
            }
            
            .section {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #ddd;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar"></div>
    
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="confidential">内部资料 注意保密</div>
            <div class="year">2025 年度 LinkedIn 新升级</div>
            <h1 class="main-title">询盘倍增器：领英外贸高手进阶</h1>
            <div class="subtitle">LinkedIn 外贸营销完全指南</div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <button class="nav-toggle" onclick="toggleSidebar()">📚 目录</button>
            <div class="nav-search">
                <input type="text" id="searchInput" placeholder="搜索内容..." onkeyup="searchContent()">
            </div>
            <div class="nav-tools" id="navTools">
                <button class="nav-btn" onclick="toggleNightMode()">🌙 夜间</button>
                <button class="nav-btn" onclick="window.print()">🖨️ 打印</button>
                <button class="nav-btn" onclick="toggleFontSize()">🔤 字体</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 侧边栏目录 -->
            <div class="sidebar" id="sidebar">
                <div class="toc">
                    <h3>📚 目录导航</h3>
                    
                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第一部分：LinkedIn 基础与账号优化</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-1-1')">
                                <span class="chapter-number">1.1</span>LinkedIn 的基础功能与外贸潜力
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-2')">
                                <span class="chapter-number">1.2</span>账号注册与职业定位
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-3')">
                                <span class="chapter-number">1.3</span>如何选择高效的个人主页照片和封面
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-4')">
                                <span class="chapter-number">1.4</span>优化个人简介：突出行业关键词
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-5')">
                                <span class="chapter-number">1.5</span>完善工作经历和项目经验
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-6')">
                                <span class="chapter-number">1.6</span>技能标签与证书展示的设置技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-7')">
                                <span class="chapter-number">1.7</span>如何撰写吸引客户的个人概要
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-8')">
                                <span class="chapter-number">1.8</span>公司主页的搭建与品牌形象优化
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-9')">
                                <span class="chapter-number">1.9</span>掌握 LinkedIn 搜索功能：精准找到潜在客户
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-1-10')">
                                <span class="chapter-number">1.10</span>使用 LinkedIn Premium 和 Sales Navigator 的优劣分析
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第二部分：内容营销与品牌塑造</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-2-1')">
                                <span class="chapter-number">2.1</span>LinkedIn 内容算法解析
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-2')">
                                <span class="chapter-number">2.2</span>撰写高互动率的帖子：选题与节奏
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-3')">
                                <span class="chapter-number">2.3</span>视频内容的制作与发布技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-4')">
                                <span class="chapter-number">2.4</span>行业趋势文章：如何以专家身份吸引客户
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-5')">
                                <span class="chapter-number">2.5</span>如何策划月度内容主题并保持一致性
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-6')">
                                <span class="chapter-number">2.6</span>用户评论和互动：建立信任感
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-7')">
                                <span class="chapter-number">2.7</span>高效利用文档分享功能展示产品价值
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-8')">
                                <span class="chapter-number">2.8</span>客户痛点分析与内容切入点选择
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-9')">
                                <span class="chapter-number">2.9</span>长文与短文：不同形式的内容策略
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-10')">
                                <span class="chapter-number">2.10</span>创建病毒式传播的内容技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-11')">
                                <span class="chapter-number">2.11</span>如何用热点新闻与话题提升曝光
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-2-22')">
                                <span class="chapter-number">2.22</span>如何通过内容引导询盘
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第三部分：客户开发与主动联系策略</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-3-1')">
                                <span class="chapter-number">3.1</span>精准定位目标客户的搜索技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-2')">
                                <span class="chapter-number">3.2</span>添加客户的个性化连接请求方法
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-3')">
                                <span class="chapter-number">3.3</span>初次接触客户的高效开场白
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-4')">
                                <span class="chapter-number">3.4</span>如何在 LinkedIn 聊天中建立信任感
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-5')">
                                <span class="chapter-number">3.5</span>客户跟进与 CRM 工具结合使用
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-6')">
                                <span class="chapter-number">3.6</span>分析客户的行为：谁看了你的资料？
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-7')">
                                <span class="chapter-number">3.7</span>如何处理客户的冷淡回应
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-8')">
                                <span class="chapter-number">3.8</span>制作引流话术并引导客户到邮件沟通
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-9')">
                                <span class="chapter-number">3.9</span>针对不同文化背景的沟通技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-10')">
                                <span class="chapter-number">3.10</span>高效解决客户的常见顾虑
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-3-11')">
                                <span class="chapter-number">3.11</span>从沟通到下单：如何推动客户成交
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第四部分：专业领域展示与社交信任建立</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-4-1')">
                                <span class="chapter-number">4.1</span>如何加入行业群组并建立影响力
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-2')">
                                <span class="chapter-number">4.2</span>在群组中分享有价值的内容
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-3')">
                                <span class="chapter-number">4.3</span>通过 LinkedIn 活动页面推广产品或服务
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-4')">
                                <span class="chapter-number">4.4</span>推荐信和客户评价的重要性及获取方法
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-5')">
                                <span class="chapter-number">4.5</span>如何利用共同联系打造专业信任度
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-6')">
                                <span class="chapter-number">4.6</span>与潜在客户共同创建文章的方法
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-7')">
                                <span class="chapter-number">4.7</span>利用 LinkedIn Pulse 发布专业行业文章
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-8')">
                                <span class="chapter-number">4.8</span>定期更新并优化个人主页以保持专业形象
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-9')">
                                <span class="chapter-number">4.9</span>客户成功案例展示与影响力扩大
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-10')">
                                <span class="chapter-number">4.10</span>从"个人品牌"到"行业领袖"的升级路线
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-4-11')">
                                <span class="chapter-number">4.11</span>如何成为 LinkedIn 的 Top Voice
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第五部分：竞争对手与市场调研</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-5-1')">
                                <span class="chapter-number">5.1</span>如何监控竞争对手的 LinkedIn 活动
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-2')">
                                <span class="chapter-number">5.2</span>发现市场趋势并快速调整策略
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-3')">
                                <span class="chapter-number">5.3</span>使用 LinkedIn 数据分析目标市场
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-4')">
                                <span class="chapter-number">5.4</span>评估竞争对手的内容策略
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-5')">
                                <span class="chapter-number">5.5</span>如何借鉴竞争对手的优质案例
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-6')">
                                <span class="chapter-number">5.6</span>开展市场问卷调查并吸引客户参与
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-7')">
                                <span class="chapter-number">5.7</span>通过客户的职业轨迹分析需求变化
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-8')">
                                <span class="chapter-number">5.8</span>收集客户反馈优化服务流程
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-9')">
                                <span class="chapter-number">5.9</span>如何定位高潜力市场
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-5-10')">
                                <span class="chapter-number">5.10</span>评估市场机会与风险
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第六部分：提升互动与粉丝增长</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-6-1')">
                                <span class="chapter-number">6.1</span>如何鼓励客户参与内容互动
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-2')">
                                <span class="chapter-number">6.2</span>组织有奖活动增加粉丝量
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-3')">
                                <span class="chapter-number">6.3</span>高效利用 LinkedIn 广告推广内容
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-4')">
                                <span class="chapter-number">6.4</span>增加个人资料浏览量的小技巧
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-5')">
                                <span class="chapter-number">6.5</span>借助话题标签吸引更多关注
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-6')">
                                <span class="chapter-number">6.6</span>提高内容互动率的具体策略
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-7')">
                                <span class="chapter-number">6.7</span>如何策划季度增长计划
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-8')">
                                <span class="chapter-number">6.8</span>制作潜在客户的"引流工具"
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-9')">
                                <span class="chapter-number">6.9</span>推动客户主动向同事或朋友推荐
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-10')">
                                <span class="chapter-number">6.10</span>保持增长势头的持续优化
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-6-11')">
                                <span class="chapter-number">6.11</span>如何转化粉丝为真实客户
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第七部分：高级工具与实用技巧</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-7-1')">
                                <span class="chapter-number">7.1</span>使用 LinkedIn Analytics 优化表现
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-2')">
                                <span class="chapter-number">7.2</span>如何导出客户数据并跟进
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-3')">
                                <span class="chapter-number">7.3</span>借助外部工具批量处理客户信息
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-4')">
                                <span class="chapter-number">7.4</span>自动化营销工具在 LinkedIn 的使用
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-5')">
                                <span class="chapter-number">7.5</span>用 InMail 提升联系效率
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-6')">
                                <span class="chapter-number">7.6</span>多账号协作开发潜在客户
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-7')">
                                <span class="chapter-number">7.7</span>客户数据的整理与分级管理
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-8')">
                                <span class="chapter-number">7.8</span>结合其他平台如 WhatsApp 的高效引流
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-7-9')">
                                <span class="chapter-number">7.9</span>利用 LinkedIn API 整合客户资源
                            </div>
                        </div>
                    </div>

                    <div class="part">
                        <div class="part-title" onclick="togglePart(this)">第八部分：案例解析与实战演练</div>
                        <div class="chapters">
                            <div class="chapter" onclick="scrollToSection('section-8-1')">
                                <span class="chapter-number">8.1</span>成功获取百万订单的案例分享
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-8-2')">
                                <span class="chapter-number">8.2</span>分析失败案例的教训与改进
                            </div>
                            <div class="chapter" onclick="scrollToSection('section-8-3')">
                                <span class="chapter-number">8.3</span>实战演练：设计自己的 LinkedIn 外贸开发计划
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" id="content">
                <!-- 前言 -->
                <div class="section" id="section-preface">
                    <h2>📖 前言</h2>
                    <p>在今天的全球化商业环境中，LinkedIn 已经成为外贸行业不可忽视的重要工具。从最初的社交平台，到如今的跨境商务桥梁，LinkedIn 不仅为外贸人提供了一个展示自我和产品的窗口，更为全球买家和卖家提供了一个高效、精准的交流平台。然而，如何在这个平台上脱颖而出、获取有效询盘，并将潜在客户转化为长期合作伙伴，仍然是许多外贸从业者面临的挑战。</p>

                    <p>本书《询盘倍增器：领英外贸高手进阶》，全面解析如何利用 LinkedIn 这一平台进行精准客户开发，从而提升询盘转化率、扩大国际市场份额。书中的每一节课都经过精心设计，涵盖了从账号优化、内容营销、客户开发，到高级工具使用、市场调研等各个方面的实战技巧。通过逐步推进的学习，您将能够深入了解如何借助 LinkedIn 的功能与策略，将线上交流转化为实际的业务成果。</p>

                    <p>不论您是刚刚踏入外贸行业的新手，还是已经拥有一定经验的外贸专家，本书都能为您提供可操作的策略与技巧，帮助您在激烈的市场竞争中占得先机。随着每一课的深入，您将发现，LinkedIn 不仅是一个社交工具，它更是您通向成功外贸事业的强大引擎。</p>
                </div>

                <!-- 第一部分：LinkedIn 基础与账号优化 -->
                <div class="section" id="section-1-1">
                    <h2>🚀 第一部分：LinkedIn 基础与账号优化</h2>

                    <h3>1.1 LinkedIn 的基础功能与外贸潜力</h3>
                    <p>在全球化加速的背景下，LinkedIn 不仅是职业社交的中心舞台，更成为外贸行业掘金的新阵地。从个人品牌的塑造到精准客户的开发，从专业影响力的构建到数据驱动的运营优化，LinkedIn 所蕴含的功能与潜力值得每一位外贸人深入挖掘。</p>

                    <div class="highlight">
                        <h4>🎯 个人主页：信任构建的起点</h4>
                        <p>LinkedIn 个人主页是外贸人展示专业形象的核心载体，其结构与内容决定了第一印象的质量。在外贸的语境中，个人主页不仅仅是职业背景的展示，更是对目标客户传递信任与价值的窗口。这一功能的潜力在于，它能够通过专业化、个性化的内容编排，直接传递"你能解决客户什么问题"的关键信息。</p>
                        <p>标题、简介、背景图片，这些看似简单的元素，却能深刻影响潜在客户的决策。尤其在外贸行业，客户对合作伙伴的第一印象往往决定了其后续的沟通意愿。个人主页在无声中传递了专业素养、行业资源与服务能力的复合信号，使客户在信息碎片化的环境中迅速对你形成清晰认知。</p>
                    </div>

                    <h4>🔍 搜索功能：连接全球的桥梁</h4>
                    <p>LinkedIn 的搜索功能在外贸领域拥有不可忽视的价值。它不仅是连接全球商业资源的技术支撑，更是精细化客户开发的关键环节。在外贸市场中，客户分布跨越多个国家、地区与行业，传统的线下展会、第三方代理模式逐渐被数字化工具所替代。而 LinkedIn 的搜索功能通过关键词与筛选条件的多维组合，使外贸人能够在全球范围内精准定位潜在客户。</p>

                    <p>这一功能的意义不仅在于找到客户，更在于通过数据化手段洞察市场趋势，优化客户画像。外贸人通过 LinkedIn 的搜索能力，可以构建出一个涵盖目标客户的动态资源池。这种资源池不仅是订单开发的基础，也是行业竞争力的重要体现。</p>

                    <h4>📝 内容发布：专业赋能与影响力传播</h4>
                    <p>LinkedIn 的内容发布功能为外贸人提供了从信息提供者转型为行业领袖的可能性。在客户开发的过程中，传统营销手段面临着日益激烈的竞争，而专业化、场景化的内容输出则能够有效降低沟通门槛，建立信任关系。</p>

                    <p>在外贸环境中，内容发布的潜力体现在其长尾效应。高质量内容的传播不仅能够吸引直接受众，还能通过二级、三级传播扩大影响范围。尤其是行业洞察、案例分析与客户教育类内容，它们通过解决潜在客户的痛点、传递行业价值观而逐步强化品牌认知。此外，LinkedIn 的算法倾向于推广具有互动性的内容，外贸人通过精心设计的内容策略，可以最大化平台分发流量的利用效率。</p>

                    <h4>💬 互动与沟通：人性化连接的艺术</h4>
                    <p>LinkedIn 的核心价值在于其所提供的高效互动机制。与传统的电子邮件或电话沟通相比，LinkedIn 的社交属性使其在客户开发中具备更强的情感温度与人性化特征。在外贸行业，客户沟通往往因文化差异与地域距离而显得疏离，而 LinkedIn 的互动功能则为拉近双方关系提供了天然的场景优势。</p>

                    <p>这种互动不仅限于直接对话，更包括点赞、评论以及动态的实时互动。外贸人可以通过对客户发布内容的深度参与，逐步建立认同感与专业影响力。LinkedIn 的社交属性赋予了沟通更多非正式化的可能，使其在传统客户开发模式中显得更加灵活与高效。</p>

                    <h4>📊 数据分析：战略决策的依据</h4>
                    <p>LinkedIn 的数据分析功能是外贸人提升运营效率的关键抓手。在信息高度数字化的今天，决策的科学性与精准性依赖于数据的深度挖掘与解读。LinkedIn 通过对内容表现、受众特征与行为轨迹的全面统计，为外贸人提供了清晰的运营反馈与优化方向。</p>

                    <p>在外贸场景下，数据分析的价值不仅体现在过程改进上，更在于战略布局的优化。通过对客户群体的地域分布、行业偏好以及行为模式的分析，外贸人能够提前捕捉市场需求变化，调整资源分配与业务重心。同时，数据分析还能够帮助外贸人评估内容策略的有效性，为后续内容设计提供指导，从而实现资源利用的最大化。</p>

                    <div class="highlight">
                        <h4>💰 躺赚潜力：重塑外贸竞争格局</h4>
                        <p>LinkedIn 的基础功能看似简单，但其背后蕴含的潜力正在重塑外贸行业的竞争格局。从资源整合到精准营销，从信任构建到数据驱动，LinkedIn 以其高度专业化的功能为外贸人提供了跨越传统边界的全新工具。它不仅是一种平台，更是一种全新的商业逻辑，一种以专业为核心、以技术为依托的全球化商业实践方式。</p>
                    </div>
                </div>

                <div class="section" id="section-1-2">
                    <h3>1.2 账号注册与职业定位</h3>
                    <p>LinkedIn 作为职业社交平台的先驱，为外贸行业的从业者提供了突破传统模式的绝佳机会。然而，要充分发挥这一平台的潜力，外贸人必须以清晰的职业定位为核心，构建一个能够精准传递价值的专业账户。</p>

                    <p>在 LinkedIn 的生态中，注册账号的过程远不只是填写个人信息的简单步骤，而是一场对自我定位与职业规划的系统梳理。账号注册的每一环节都直接决定了你如何被潜在客户、合作伙伴甚至竞争对手感知。这不仅是一种形式上的呈现，更是一种价值与信任的塑造过程。</p>

                    <div class="highlight">
                        <h4>🆔 LinkedIn 账号注册：身份的数字化投射</h4>
                        <p>在 LinkedIn 的语境中，账号本身是外贸人专业身份的数字化体现。它不仅仅是一种存在的证明，更是一种影响力的开始。在外贸行业，客户的信任往往来自对细节的关注。账号注册的过程中，每一项填写的内容，都在传递你对细节的理解与对专业性的追求。</p>
                        <p>从选择语言到设定基础信息，LinkedIn 的账号注册环节是一场精心设计的个人品牌打造过程。尤其对于外贸人而言，账号中的每一项内容不仅是对职业生涯的总结，更是对未来合作的承诺。它在无形中构建了一种预期——客户期待从你的账号中看到清晰的行业定位、丰富的国际经验以及对问题的独特洞察。</p>
                    </div>

                    <h4>🎯 职业定位：价值主张的核心表达</h4>
                    <p>外贸人的 LinkedIn 职业定位是账号的灵魂。它贯穿于个人简介、职位描述乃至动态发布的方方面面，决定了平台中其他用户如何解读你的角色与能力。在职业社交的语境中，定位的模糊往往意味着价值的稀释。</p>

                    <p>一个清晰且具有针对性的职业定位能够帮助外贸人高效连接目标受众。对于从事建材、化工、电子等外贸领域的专业人士，职业定位不仅需要反映当前的专业背景，更应体现对目标客户需求的深刻理解。这种定位不是单纯的自我描述，而是通过客户视角重新定义自己的价值主张。</p>

                    <h4>🏆 个人品牌与行业话语权的初步构建</h4>
                    <p>LinkedIn 的独特之处在于，它不仅仅是一个展示过往成就的平台，更是一个塑造未来职业方向的舞台。在外贸行业中，职业定位的背后隐含着一套关于个人品牌的长期战略。</p>

                    <p>个人品牌的核心在于建立行业话语权，而这需要通过一系列一致性与连贯性的表达来实现。从账号的基本信息到后续的内容发布，外贸人必须始终围绕特定主题展开，确保每一次互动，每一篇文章都在强化专业认知。</p>

                    <p>这种品牌塑造并非一蹴而就，而是通过反复的信息传递与价值展现逐步建立。例如，一名从事硅酮密封剂出口业务的外贸人，其职业定位可以围绕"为全球建筑行业提供高性能解决方案"展开。在此基础上，进一步通过与行业相关的关键词布局与资源展示，奠定专业权威形象。</p>

                    <h4>🔍 细节决定成败：形象与信息的一致性</h4>
                    <p>外贸行业的 LinkedIn 用户往往需要面对不同国家、文化背景的客户，因此职业定位在语言表达、文化适配性上的一致性尤为重要。这种一致性不仅体现在内容本身，还包括视觉形象的统一。</p>

                    <p>一个专业的头像、与行业相关的背景图片，以及简洁而富有感染力的个人标题，能够迅速传递信任与专业感。细节是 LinkedIn 职业定位的关键所在。它不仅影响潜在客户的第一印象，更直接关系到后续的互动与转化。</p>

                    <p>职业定位的意义在于帮助外贸人找到"被看见"的正确方式。在 LinkedIn 这个充满竞争的社交平台上，能够被精准识别、清晰记忆并持续关注，才是职业定位的最终目的。</p>

                    <div class="highlight">
                        <h4>🔄 职业定位的动态性与长期性</h4>
                        <p>外贸人的职业定位并非一成不变，而是一个随行业趋势与个人发展不断演进的动态过程。在 LinkedIn 平台上，职业定位的变化应与行业需求同步，通过对市场与客户反馈的敏锐捕捉，持续优化自身的表达方式与内容结构。</p>
                        <p>这种动态性不仅体现了职业人的适应力与创新力，也为客户提供了不断更新的价值体验。而长期性的职业定位则帮助外贸人在竞争激烈的市场中，逐步积累品牌认知与行业影响力，最终将 LinkedIn 从一项工具转化为一项战略资产。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">↑</button>

    <script>
        // 全局变量
        let isNightMode = false;
        let fontSize = 16;
        let isSidebarVisible = window.innerWidth > 768;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgressBar();
            updateBackToTopButton();
            loadContent();
            initializeParts();

            // 默认展开第一部分
            const firstPart = document.querySelector('.part .chapters');
            if (firstPart) {
                firstPart.classList.add('expanded');
            }
        });

        // 滚动事件监听
        window.addEventListener('scroll', function() {
            updateProgressBar();
            updateBackToTopButton();
        });

        // 窗口大小改变事件
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('sidebar').style.display = 'block';
                isSidebarVisible = true;
            } else {
                document.getElementById('sidebar').style.display = 'none';
                isSidebarVisible = false;
            }
        });

        // 更新进度条
        function updateProgressBar() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.offsetHeight;
            const winHeight = window.innerHeight;
            const scrollPercent = scrollTop / (docHeight - winHeight);
            const scrollPercentRounded = Math.round(scrollPercent * 100);

            document.querySelector('.progress-bar').style.width = Math.min(scrollPercentRounded, 100) + '%';
        }

        // 更新返回顶部按钮
        function updateBackToTopButton() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }

        // 返回顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 滚动到指定章节
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                const navBarHeight = document.querySelector('.nav-bar').offsetHeight;
                const targetPosition = section.offsetTop - navBarHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // 高亮当前章节
                highlightCurrentSection(sectionId);

                // 在移动端点击后隐藏侧边栏
                if (window.innerWidth <= 768) {
                    toggleSidebar();
                }
            }
        }

        // 高亮当前章节
        function highlightCurrentSection(sectionId) {
            // 移除所有高亮
            document.querySelectorAll('.chapter').forEach(chapter => {
                chapter.style.background = '';
                chapter.style.fontWeight = '';
            });

            // 高亮当前章节
            const chapters = document.querySelectorAll('.chapter');
            chapters.forEach(chapter => {
                if (chapter.getAttribute('onclick').includes(sectionId)) {
                    chapter.style.background = '#e3f2fd';
                    chapter.style.fontWeight = 'bold';
                }
            });
        }

        // 切换部分展开/收起
        function togglePart(partTitle) {
            const chapters = partTitle.nextElementSibling;
            chapters.classList.toggle('expanded');

            // 更新箭头图标
            if (chapters.classList.contains('expanded')) {
                partTitle.style.background = '#005885';
            } else {
                partTitle.style.background = '#0077b5';
            }
        }

        // 初始化所有部分
        function initializeParts() {
            const partTitles = document.querySelectorAll('.part-title');
            partTitles.forEach(title => {
                title.innerHTML += ' <span style="float: right;">▼</span>';
            });
        }

        // 切换侧边栏显示
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth <= 768) {
                if (sidebar.style.display === 'none' || sidebar.style.display === '') {
                    sidebar.style.display = 'block';
                    sidebar.classList.add('mobile-visible');
                } else {
                    sidebar.style.display = 'none';
                    sidebar.classList.remove('mobile-visible');
                }
            }
        }

        // 搜索功能
        function searchContent() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const sections = document.querySelectorAll('.section');
            const chapters = document.querySelectorAll('.chapter');

            if (searchTerm === '') {
                // 显示所有内容
                sections.forEach(section => {
                    section.style.display = 'block';
                });
                chapters.forEach(chapter => {
                    chapter.style.display = 'block';
                });
                return;
            }

            // 搜索内容区域
            sections.forEach(section => {
                const text = section.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    section.style.display = 'block';
                    // 高亮搜索词
                    highlightSearchTerm(section, searchTerm);
                } else {
                    section.style.display = 'none';
                }
            });

            // 搜索目录
            chapters.forEach(chapter => {
                const text = chapter.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    chapter.style.display = 'block';
                    chapter.style.background = '#fff3cd';
                } else {
                    chapter.style.display = 'none';
                }
            });
        }

        // 高亮搜索词
        function highlightSearchTerm(element, term) {
            // 这里可以添加更复杂的高亮逻辑
            element.style.border = '2px solid #ffc107';
            setTimeout(() => {
                element.style.border = '';
            }, 3000);
        }

        // 切换夜间模式
        function toggleNightMode() {
            isNightMode = !isNightMode;
            const body = document.body;
            const container = document.querySelector('.container');
            const sections = document.querySelectorAll('.section');
            const sidebar = document.querySelector('.sidebar');
            const navBar = document.querySelector('.nav-bar');

            if (isNightMode) {
                body.style.background = 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)';
                body.style.color = '#ecf0f1';
                container.style.background = '#34495e';
                container.style.color = '#ecf0f1';

                sections.forEach(section => {
                    section.style.background = '#2c3e50';
                    section.style.color = '#ecf0f1';
                });

                if (sidebar) {
                    sidebar.style.background = '#2c3e50';
                    sidebar.style.color = '#ecf0f1';
                }

                if (navBar) {
                    navBar.style.background = '#2c3e50';
                    navBar.style.color = '#ecf0f1';
                }

                document.querySelector('.nav-btn').textContent = '☀️ 日间';
            } else {
                body.style.background = 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';
                body.style.color = '#333';
                container.style.background = 'white';
                container.style.color = '#333';

                sections.forEach(section => {
                    section.style.background = 'white';
                    section.style.color = '#333';
                });

                if (sidebar) {
                    sidebar.style.background = '#f8f9fa';
                    sidebar.style.color = '#333';
                }

                if (navBar) {
                    navBar.style.background = '#f8f9fa';
                    navBar.style.color = '#333';
                }

                document.querySelector('.nav-btn').textContent = '🌙 夜间';
            }
        }

        // 切换字体大小
        function toggleFontSize() {
            fontSize = fontSize === 16 ? 18 : fontSize === 18 ? 20 : 16;
            document.body.style.fontSize = fontSize + 'px';

            const btn = document.querySelector('.nav-btn:last-child');
            btn.textContent = fontSize === 16 ? '🔤 字体' : fontSize === 18 ? '🔤 大' : '🔤 超大';
        }

        // 加载内容（这里可以添加动态加载逻辑）
        function loadContent() {
            // 模拟加载动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    section.style.transition = 'all 0.5s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl + F 搜索
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }

            // ESC 清除搜索
            if (e.key === 'Escape') {
                document.getElementById('searchInput').value = '';
                searchContent();
            }

            // 空格键暂停/继续滚动
            if (e.key === ' ' && e.target.tagName !== 'INPUT') {
                e.preventDefault();
                if (window.scrollY > 0) {
                    scrollToTop();
                }
            }
        });

        // 触摸手势支持（移动端）
        let touchStartY = 0;
        let touchEndY = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartY = e.changedTouches[0].screenY;
        });

        document.addEventListener('touchend', function(e) {
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // 向上滑动 - 可以添加向下翻页逻辑
                } else {
                    // 向下滑动 - 可以添加向上翻页逻辑
                }
            }
        }

        // 自动保存阅读进度
        function saveReadingProgress() {
            const scrollPercent = (window.pageYOffset / (document.body.offsetHeight - window.innerHeight)) * 100;
            localStorage.setItem('linkedin_book_progress', scrollPercent);
        }

        // 恢复阅读进度
        function restoreReadingProgress() {
            const savedProgress = localStorage.getItem('linkedin_book_progress');
            if (savedProgress) {
                const targetScroll = (parseFloat(savedProgress) / 100) * (document.body.offsetHeight - window.innerHeight);
                window.scrollTo(0, targetScroll);
            }
        }

        // 页面卸载时保存进度
        window.addEventListener('beforeunload', saveReadingProgress);

        // 页面加载完成后恢复进度
        window.addEventListener('load', function() {
            setTimeout(restoreReadingProgress, 1000);
        });
    </script>
</body>
</html>
